import OpenAI from "openai";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { RSI, EMA, WMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";
import dayjs from "dayjs";

// Chart.js + plugins
import {
  Chart,
  TimeScale,
  LinearScale,
  BarController,
  BarElement,
  LineController,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  Title,
} from "chart.js";
// Note: Using linear scale for now due to date adapter issues in ES modules

// Dùng canvas để ghép nhiều biểu đồ
import { createCanvas, loadImage } from "canvas";

// Import new classes
import {
  BinanceAPI,
  binanceAPI,
  fetchTimeframeData,
  getCachedData,
  fetchAllTimeframes,
  fetchData,
  TIMEFRAMES,
  dataCache
} from "./src/BinanceAPI.js";

import {
  TelegramBot,
  telegramBot,
  bot,
  chatId
} from "./src/TelegramBot.js";

dotenv.config();

// === Config ===
// Symbol is now taken from binanceAPI.getSymbol() dynamically based on commands
const INTERVAL = process.env.INTERVAL || "1h";
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Start Telegram bot to listen for commands
try {
  telegramBot.onSymbolCommand(async (ctx, symbol, chatContext) => {
    const chatId = ctx.chat?.id;
    try {
      // Validate symbol by creating a temporary BinanceAPI instance
      const tempBinanceAPI = new BinanceAPI({ symbol });
      await tempBinanceAPI.getCachedData('15m');

      // Run full AI vision analysis with chat context
      await runBot(symbol, chatId);
    } catch (err) {
      console.error(`Symbol command handling failed for chat ${chatId}:`, err?.message || err);
      try {
        await ctx.reply('❌ Không thể phân tích ký hiệu này. Hãy dùng ví dụ: BTC hoặc BTCUSDT (Binance Spot).');
      } catch {}
    }
  });

  // Launch bot polling to receive messages
  telegramBot.start();
} catch (e) {
  console.error('Failed to start Telegram bot listener:', e?.message || e);
}

// === Custom Candlestick Plugin ===
const CandlestickPlugin = {
  id: 'candlestick',
  afterDatasetsDraw(chart) {
    const { ctx, data, scales } = chart;
    const dataset = data.datasets.find(ds => ds.label && ds.label.includes(binanceAPI.getSymbol()));
    if (!dataset || !dataset.data[0] || dataset.data[0].o === undefined) return;

    const xScale = scales.x;
    const yScale = scales.y;

    dataset.data.forEach((candle, index) => {
      if (!candle || candle.o === undefined) return;

      const x = xScale.getPixelForValue(candle.x);
      const yOpen = yScale.getPixelForValue(candle.o);
      const yHigh = yScale.getPixelForValue(candle.h);
      const yLow = yScale.getPixelForValue(candle.l);
      const yClose = yScale.getPixelForValue(candle.c);

      const isUp = candle.c >= candle.o;
      const candleWidth = Math.max(2, (xScale.width / dataset.data.length) * 0.6);

      // TradingView-style colors
      const upColor = '#26a69a';      // Green for bullish candles
      const downColor = '#ef5350';    // Red for bearish candles
      const upBorderColor = '#26a69a';
      const downBorderColor = '#ef5350';

      ctx.save();

      // Draw the wick (high-low line)
      ctx.strokeStyle = isUp ? upColor : downColor;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, yHigh);
      ctx.lineTo(x, yLow);
      ctx.stroke();

      // Draw the body (open-close rectangle)
      const bodyTop = Math.min(yOpen, yClose);
      const bodyHeight = Math.max(1, Math.abs(yClose - yOpen)); // Ensure minimum height

      if (isUp) {
        // Bullish candle - filled with up color
        ctx.fillStyle = upColor;
        ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = upBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
      } else {
        // Bearish candle - filled with down color
        ctx.fillStyle = downColor;
        ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = downBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
      }

      ctx.restore();
    });
  }
};

// === Common chart register (gọi trong chartCallback của ChartJSNodeCanvas) ===
const registerAll = (Chart) => {
  Chart.register(
    TimeScale,
    LinearScale,
    BarController,
    BarElement,
    LineController,
    LineElement,
    PointElement,
    Tooltip,
    Legend,
    Title,
    CandlestickPlugin
  );
};

// === Multi-Timeframe Data Fetching ===
// These functions are now handled by BinanceAPI class but kept for backward compatibility
// The actual implementations are imported from ./src/BinanceAPI.js

// === Indicators (EMA, Sonic R PAC, RSI + EMA9 + WMA45) ===
function padToLen(arr, len) {
  return Array(len - arr.length).fill(null).concat(arr);
}

function calculateIndicators(candles) {
  const closes = candles.map((c) => c.close);
  const highs = candles.map((c) => c.high);
  const lows = candles.map((c) => c.low);
  const len = candles.length;

  // Sonic R PAC (EMA 34 của high/low/close)
  const pacLen = 34;
  const pacC = padToLen(EMA.calculate({ values: closes, period: pacLen }), len);
  const pacH = padToLen(EMA.calculate({ values: highs, period: pacLen }), len);
  const pacL = padToLen(EMA.calculate({ values: lows, period: pacLen }), len);

  // EMA
  const ema20 = padToLen(EMA.calculate({ values: closes, period: 20 }), len);
  const ema50 = padToLen(EMA.calculate({ values: closes, period: 50 }), len);
  const ema89 = padToLen(EMA.calculate({ values: closes, period: 89 }), len);
  const ema200 = padToLen(EMA.calculate({ values: closes, period: 200 }), len);
  const ema610 = padToLen(EMA.calculate({ values: closes, period: 610 }), len);

  // RSI + EMA9 + WMA45
  const rsi14 = RSI.calculate({ values: closes, period: 14 });
  const rsi = padToLen(rsi14, len);
  const rsiEma9 = padToLen(EMA.calculate({ values: rsi14, period: 9 }), len);
  const rsiWma45 = padToLen(WMA.calculate({ values: rsi14, period: 45 }), len);

  return { closes, pacC, pacH, pacL, ema20, ema50, ema89, ema200, ema610, rsi, rsiEma9, rsiWma45 };
}

// === Trend Analysis Engine ===
function analyzeTrend(candles, indicators) {
  if (!candles || candles.length < 50) {
    return { trend: 'INSUFFICIENT_DATA', strength: 0, confidence: 0 };
  }

  const { ema20, ema50, ema89, ema200, closes, rsi } = indicators;
  const currentPrice = closes[closes.length - 1];
  const currentEma20 = ema20[ema20.length - 1];
  const currentEma50 = ema50[ema50.length - 1];
  const currentEma89 = ema89[ema89.length - 1];
  const currentEma200 = ema200[ema200.length - 1];
  const currentRsi = rsi[rsi.length - 1];

  // EMA alignment analysis
  const emaAlignment = analyzeEmaAlignment(currentPrice, currentEma20, currentEma50, currentEma89, currentEma200);

  // Price action analysis
  const priceAction = analyzePriceAction(candles.slice(-20)); // Last 20 candles

  // RSI momentum analysis
  const rsiMomentum = analyzeRsiMomentum(currentRsi);

  // Volume analysis
  const volumeAnalysis = analyzeVolume(candles.slice(-10)); // Last 10 candles

  // Combine all factors to determine trend
  const trendScore = calculateTrendScore(emaAlignment, priceAction, rsiMomentum, volumeAnalysis);

  return {
    trend: determineTrend(trendScore.score),
    strength: Math.abs(trendScore.score),
    confidence: trendScore.confidence,
    details: {
      emaAlignment,
      priceAction,
      rsiMomentum,
      volumeAnalysis,
      score: trendScore.score
    }
  };
}

function analyzeEmaAlignment(price, ema20, ema50, ema89, ema200) {
  let score = 0;
  let alignmentCount = 0;

  // Check EMA hierarchy for bullish/bearish alignment
  if (ema20 > ema50) { score += 1; alignmentCount++; }
  if (ema50 > ema89) { score += 1; alignmentCount++; }
  if (ema89 > ema200) { score += 1; alignmentCount++; }
  if (price > ema20) { score += 2; alignmentCount += 2; } // Price above short EMA is more important

  // Check for bearish alignment
  if (ema20 < ema50) { score -= 1; alignmentCount++; }
  if (ema50 < ema89) { score -= 1; alignmentCount++; }
  if (ema89 < ema200) { score -= 1; alignmentCount++; }
  if (price < ema20) { score -= 2; alignmentCount += 2; }

  return {
    score: alignmentCount > 0 ? score / alignmentCount : 0,
    strength: Math.abs(score) / Math.max(alignmentCount, 1)
  };
}

function analyzePriceAction(recentCandles) {
  if (recentCandles.length < 10) return { score: 0, pattern: 'INSUFFICIENT_DATA' };

  let higherHighs = 0;
  let lowerLows = 0;
  let bullishCandles = 0;
  let bearishCandles = 0;

  for (let i = 1; i < recentCandles.length; i++) {
    const current = recentCandles[i];
    const previous = recentCandles[i - 1];

    // Count higher highs and lower lows
    if (current.high > previous.high) higherHighs++;
    if (current.low < previous.low) lowerLows++;

    // Count bullish/bearish candles
    if (current.close > current.open) bullishCandles++;
    if (current.close < current.open) bearishCandles++;
  }

  const totalCandles = recentCandles.length - 1;
  const hhPercentage = higherHighs / totalCandles;
  const llPercentage = lowerLows / totalCandles;
  const bullishPercentage = bullishCandles / totalCandles;

  let score = 0;
  let pattern = 'SIDEWAYS';

  if (hhPercentage > 0.6 && bullishPercentage > 0.6) {
    score = (hhPercentage + bullishPercentage) / 2;
    pattern = 'BULLISH_MOMENTUM';
  } else if (llPercentage > 0.6 && bullishPercentage < 0.4) {
    score = -((llPercentage + (1 - bullishPercentage)) / 2);
    pattern = 'BEARISH_MOMENTUM';
  }

  return { score, pattern, higherHighs, lowerLows, bullishCandles, bearishCandles };
}

function analyzeRsiMomentum(rsi) {
  if (!rsi || rsi < 0 || rsi > 100) return { score: 0, condition: 'INVALID' };

  let score = 0;
  let condition = 'NEUTRAL';

  if (rsi > 70) {
    score = -0.5; // Overbought - potential bearish
    condition = 'OVERBOUGHT';
  } else if (rsi < 30) {
    score = 0.5; // Oversold - potential bullish
    condition = 'OVERSOLD';
  } else if (rsi > 50) {
    score = (rsi - 50) / 100; // Bullish momentum
    condition = 'BULLISH_MOMENTUM';
  } else {
    score = (rsi - 50) / 100; // Bearish momentum
    condition = 'BEARISH_MOMENTUM';
  }

  return { score, condition, value: rsi };
}

function analyzeVolume(recentCandles) {
  if (recentCandles.length < 5) return { score: 0, trend: 'INSUFFICIENT_DATA' };

  const volumes = recentCandles.map(c => c.volume);
  const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
  const recentVolume = volumes[volumes.length - 1];

  let score = 0;
  let trend = 'NORMAL';

  if (recentVolume > avgVolume * 1.5) {
    score = 0.3; // High volume supports trend
    trend = 'HIGH_VOLUME';
  } else if (recentVolume < avgVolume * 0.5) {
    score = -0.1; // Low volume weakens trend
    trend = 'LOW_VOLUME';
  }

  return { score, trend, current: recentVolume, average: avgVolume };
}

function calculateTrendScore(emaAlignment, priceAction, rsiMomentum, volumeAnalysis) {
  // Weighted combination of all factors
  const weights = {
    ema: 0.4,      // EMA alignment is most important
    price: 0.3,    // Price action is second
    rsi: 0.2,      // RSI momentum
    volume: 0.1    // Volume confirmation
  };

  const score = (
    emaAlignment.score * weights.ema +
    priceAction.score * weights.price +
    rsiMomentum.score * weights.rsi +
    volumeAnalysis.score * weights.volume
  );

  // Calculate confidence based on alignment of factors
  const factors = [emaAlignment.score, priceAction.score, rsiMomentum.score];
  const avgFactor = factors.reduce((sum, f) => sum + f, 0) / factors.length;
  const variance = factors.reduce((sum, f) => sum + Math.pow(f - avgFactor, 2), 0) / factors.length;
  const confidence = Math.max(0, Math.min(1, 1 - variance)); // Lower variance = higher confidence

  return { score, confidence };
}

function determineTrend(score) {
  if (score > 0.3) return 'BULLISH';
  if (score < -0.3) return 'BEARISH';
  return 'SIDEWAYS';
}

// === Multi-Timeframe Analysis ===
async function performMultiTimeframeAnalysis(symbol = null) {
  try {
    const analysisSymbol = symbol || binanceAPI.getSymbol();
    console.log(`📊 Performing multi-timeframe analysis for ${analysisSymbol}...`);

    // Create a temporary BinanceAPI instance for this symbol if needed
    const apiInstance = symbol ? new BinanceAPI({ symbol }) : binanceAPI;

    // Fetch all timeframe data
    const allData = await apiInstance.fetchAllTimeframes();

    // Calculate indicators for each timeframe
    const analysis = {};
    for (const [timeframe, candles] of Object.entries(allData)) {
      const indicators = calculateIndicators(candles);
      const trend = analyzeTrend(candles, indicators);

      analysis[timeframe] = {
        candles,
        indicators,
        trend,
        timeframeName: TIMEFRAMES[timeframe].name,
        symbol: analysisSymbol
      };
    }

    // Add risk levels to 15m analysis
    if (analysis['15m'] && analysis['1h']) {
      const majorTrend = { trend: 'BULLISH', strength: 0.5 }; // Temporary for risk calculation
      analysis['15m'].riskLevels = calculateRiskLevels(
        analysis['15m'].candles,
        analysis['1h'].candles,
        majorTrend
      );
    }

    console.log(`✅ Multi-timeframe analysis completed for ${analysisSymbol}`);
    return analysis;
  } catch (error) {
    console.error(`❌ Multi-timeframe analysis failed for ${symbol || 'default symbol'}:`, error);
    throw error;
  }
}

// === Hierarchical Trading Logic ===
function generateTradingSignal(multiTimeframeAnalysis) {
  const { '4h': tf4h, '1h': tf1h, '15m': tf15m } = multiTimeframeAnalysis;

  // Step 1: Determine major trend from 4H and 1H
  const majorTrend = determineMajorTrend(tf4h.trend, tf1h.trend);

  // Step 2: Check for trend alignment
  const trendAlignment = checkTrendAlignment(tf4h.trend, tf1h.trend, tf15m.trend);

  // Step 3: Generate entry signal based on 15M timeframe
  const entrySignal = generateEntrySignal(tf15m, majorTrend);

  // Step 4: Calculate risk management levels
  const riskLevels = calculateRiskLevels(tf15m.candles, tf1h.candles, majorTrend);

  return {
    majorTrend,
    trendAlignment,
    entrySignal,
    riskLevels,
    confidence: calculateOverallConfidence(tf4h.trend, tf1h.trend, tf15m.trend),
    recommendation: generateTradeRecommendation(majorTrend, trendAlignment, entrySignal)
  };
}

function determineMajorTrend(trend4h, trend1h) {
  // 4H trend has higher weight than 1H
  const weights = { '4h': 0.7, '1h': 0.3 };

  // Convert trends to numeric scores
  const trendToScore = { 'BULLISH': 1, 'SIDEWAYS': 0, 'BEARISH': -1 };

  const score4h = trendToScore[trend4h.trend] || 0;
  const score1h = trendToScore[trend1h.trend] || 0;

  const weightedScore = (score4h * weights['4h']) + (score1h * weights['1h']);

  let majorTrend = 'SIDEWAYS';
  if (weightedScore > 0.3) majorTrend = 'BULLISH';
  else if (weightedScore < -0.3) majorTrend = 'BEARISH';

  return {
    trend: majorTrend,
    strength: Math.abs(weightedScore),
    components: {
      '4h': { trend: trend4h.trend, strength: trend4h.strength, confidence: trend4h.confidence },
      '1h': { trend: trend1h.trend, strength: trend1h.strength, confidence: trend1h.confidence }
    }
  };
}

function checkTrendAlignment(trend4h, trend1h, trend15m) {
  const trends = [trend4h.trend, trend1h.trend, trend15m.trend];
  const bullishCount = trends.filter(t => t === 'BULLISH').length;
  const bearishCount = trends.filter(t => t === 'BEARISH').length;
  const sidewaysCount = trends.filter(t => t === 'SIDEWAYS').length;

  let alignment = 'MIXED';
  let strength = 0;

  if (bullishCount >= 2) {
    alignment = 'BULLISH_ALIGNED';
    strength = bullishCount / 3;
  } else if (bearishCount >= 2) {
    alignment = 'BEARISH_ALIGNED';
    strength = bearishCount / 3;
  } else if (sidewaysCount >= 2) {
    alignment = 'SIDEWAYS_ALIGNED';
    strength = sidewaysCount / 3;
  }

  return {
    alignment,
    strength,
    details: {
      bullish: bullishCount,
      bearish: bearishCount,
      sideways: sidewaysCount
    }
  };
}

function generateEntrySignal(tf15m, majorTrend) {
  const { trend, indicators, candles } = tf15m;
  const currentPrice = candles[candles.length - 1].close;
  const ema20 = indicators.ema20[indicators.ema20.length - 1];
  const ema50 = indicators.ema50[indicators.ema50.length - 1];
  const rsi = indicators.rsi[indicators.rsi.length - 1];

  let signal = 'NO_SIGNAL';
  let entryPrice = null;
  let reasoning = [];

  // Only generate signals that align with major trend
  if (majorTrend.trend === 'BULLISH' && trend.trend !== 'BEARISH') {
    // Look for bullish entry conditions
    if (currentPrice > ema20 && ema20 > ema50 && rsi > 40 && rsi < 70) {
      signal = 'LONG';
      entryPrice = currentPrice;
      reasoning.push('Price above EMA20', 'EMA20 > EMA50', 'RSI in bullish range');
    } else if (currentPrice < ema20 && currentPrice > ema50 && rsi < 50) {
      signal = 'LONG_PULLBACK';
      entryPrice = ema20; // Wait for pullback to EMA20
      reasoning.push('Pullback to EMA20 in uptrend', 'RSI oversold');
    }
  } else if (majorTrend.trend === 'BEARISH' && trend.trend !== 'BULLISH') {
    // Look for bearish entry conditions
    if (currentPrice < ema20 && ema20 < ema50 && rsi < 60 && rsi > 30) {
      signal = 'SHORT';
      entryPrice = currentPrice;
      reasoning.push('Price below EMA20', 'EMA20 < EMA50', 'RSI in bearish range');
    } else if (currentPrice > ema20 && currentPrice < ema50 && rsi > 50) {
      signal = 'SHORT_PULLBACK';
      entryPrice = ema20; // Wait for pullback to EMA20
      reasoning.push('Pullback to EMA20 in downtrend', 'RSI overbought');
    }
  }

  return {
    signal,
    entryPrice,
    reasoning,
    currentPrice,
    keyLevels: {
      ema20,
      ema50,
      rsi
    }
  };
}

function calculateRiskLevels(candles15m, candles1h, majorTrend) {
  const current15m = candles15m[candles15m.length - 1];
  const recent15m = candles15m.slice(-20); // Last 20 candles for support/resistance
  const recent1h = candles1h.slice(-10);   // Last 10 1H candles for major levels

  // Calculate support and resistance levels
  const support = findSupportLevel(recent15m, recent1h);
  const resistance = findResistanceLevel(recent15m, recent1h);

  // Calculate stop loss and take profit based on trend
  let stopLoss, takeProfit1, takeProfit2;
  const atr = calculateATR(recent15m); // Average True Range for volatility

  if (majorTrend.trend === 'BULLISH') {
    stopLoss = Math.max(support, current15m.close - (atr * 2));
    takeProfit1 = current15m.close + (atr * 2);
    takeProfit2 = Math.min(resistance, current15m.close + (atr * 4));
  } else if (majorTrend.trend === 'BEARISH') {
    stopLoss = Math.min(resistance, current15m.close + (atr * 2));
    takeProfit1 = current15m.close - (atr * 2);
    takeProfit2 = Math.max(support, current15m.close - (atr * 4));
  } else {
    // Sideways market - tighter stops
    stopLoss = current15m.close + (majorTrend.trend === 'BULLISH' ? -atr : atr);
    takeProfit1 = current15m.close + (majorTrend.trend === 'BULLISH' ? atr : -atr);
    takeProfit2 = takeProfit1;
  }

  return {
    support,
    resistance,
    stopLoss,
    takeProfit1,
    takeProfit2,
    atr,
    riskReward: Math.abs(takeProfit1 - current15m.close) / Math.abs(stopLoss - current15m.close)
  };
}

function findSupportLevel(candles15m, candles1h) {
  const lows15m = candles15m.map(c => c.low);
  const lows1h = candles1h.map(c => c.low);

  // Find recent significant lows
  const support15m = Math.min(...lows15m.slice(-10));
  const support1h = Math.min(...lows1h.slice(-5));

  return Math.min(support15m, support1h);
}

function findResistanceLevel(candles15m, candles1h) {
  const highs15m = candles15m.map(c => c.high);
  const highs1h = candles1h.map(c => c.high);

  // Find recent significant highs
  const resistance15m = Math.max(...highs15m.slice(-10));
  const resistance1h = Math.max(...highs1h.slice(-5));

  return Math.max(resistance15m, resistance1h);
}

function calculateATR(candles, period = 14) {
  if (candles.length < period + 1) return 0;

  const trueRanges = [];
  for (let i = 1; i < candles.length; i++) {
    const current = candles[i];
    const previous = candles[i - 1];

    const tr = Math.max(
      current.high - current.low,
      Math.abs(current.high - previous.close),
      Math.abs(current.low - previous.close)
    );
    trueRanges.push(tr);
  }

  // Calculate average of last 'period' true ranges
  const recentTR = trueRanges.slice(-period);
  return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
}

function calculateOverallConfidence(trend4h, trend1h, trend15m) {
  const confidences = [trend4h.confidence, trend1h.confidence, trend15m.confidence];
  const avgConfidence = confidences.reduce((sum, c) => sum + c, 0) / confidences.length;

  // Boost confidence if trends align
  const trends = [trend4h.trend, trend1h.trend, trend15m.trend];
  const uniqueTrends = [...new Set(trends)];
  const alignmentBonus = uniqueTrends.length === 1 ? 0.2 : uniqueTrends.length === 2 ? 0.1 : 0;

  return Math.min(1, avgConfidence + alignmentBonus);
}

function generateTradeRecommendation(majorTrend, trendAlignment, entrySignal) {
  let recommendation = 'NO_TRADE';
  let reasoning = [];

  // High confidence trades
  if (trendAlignment.alignment.includes('ALIGNED') && entrySignal.signal !== 'NO_SIGNAL') {
    if (majorTrend.trend === 'BULLISH' && entrySignal.signal.includes('LONG')) {
      recommendation = 'STRONG_LONG';
      reasoning.push('Multi-timeframe bullish alignment', 'Clear long entry signal');
    } else if (majorTrend.trend === 'BEARISH' && entrySignal.signal.includes('SHORT')) {
      recommendation = 'STRONG_SHORT';
      reasoning.push('Multi-timeframe bearish alignment', 'Clear short entry signal');
    }
  }
  // Medium confidence trades
  else if (majorTrend.strength > 0.5 && entrySignal.signal !== 'NO_SIGNAL') {
    if (majorTrend.trend === 'BULLISH' && entrySignal.signal.includes('LONG')) {
      recommendation = 'MODERATE_LONG';
      reasoning.push('Strong major trend', 'Partial alignment');
    } else if (majorTrend.trend === 'BEARISH' && entrySignal.signal.includes('SHORT')) {
      recommendation = 'MODERATE_SHORT';
      reasoning.push('Strong major trend', 'Partial alignment');
    }
  }
  // Wait for better setup
  else {
    recommendation = 'WAIT';
    reasoning.push('Mixed signals', 'Wait for better alignment');
  }

  return {
    action: recommendation,
    reasoning,
    confidence: calculateTradeConfidence(majorTrend, trendAlignment, entrySignal)
  };
}

function calculateTradeConfidence(majorTrend, trendAlignment, entrySignal) {
  let confidence = 0;

  // Major trend strength
  confidence += majorTrend.strength * 0.4;

  // Trend alignment
  confidence += trendAlignment.strength * 0.3;

  // Entry signal quality
  if (entrySignal.signal !== 'NO_SIGNAL') {
    confidence += 0.2;
    if (entrySignal.reasoning.length >= 2) confidence += 0.1;
  }

  return Math.min(1, confidence);
}

// === Helpers ===
function inferTimeUnit(interval) {
  const s = interval.toLowerCase();
  if (s.endsWith("m")) return "minute";
  if (s.endsWith("h")) return "hour";
  if (s.endsWith("d")) return "day";
  return "hour";
}

// === Enhanced Multi-Timeframe Chart Rendering ===
async function renderPricePanel(candles, ind, width = 1200, height = 560, multiTimeframeData = null, symbol = null) {
  const displaySymbol = symbol || binanceAPI.getSymbol();
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const candleData = candles.map((c, i) => ({ x: i, o: c.open, h: c.high, l: c.low, c: c.close, time: c.time }));

  const config = {
    type: "line", // Use line as base type, candlesticks will be drawn by plugin
    data: {
      datasets: [
        {
          type: "line",
          label: `${displaySymbol} ${INTERVAL}`,
          data: candleData,
          showLine: false,
          pointRadius: 0,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        // Sonic R PAC
        {
          type: "line",
          label: "PAC High (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacH[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#ff9800',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "PAC Low (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacL[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#ff5722',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "PAC Close (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacC[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#9c27b0',
          backgroundColor: 'transparent'
        },
        // EMA overlays
        {
          type: "line",
          label: "EMA20",
          data: candles.map((_, i) => ({ x: i, y: ind.ema20[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#2196f3',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA50",
          data: candles.map((_, i) => ({ x: i, y: ind.ema50[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#4caf50',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA89",
          data: candles.map((_, i) => ({ x: i, y: ind.ema89[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#ffeb3b',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA200",
          data: candles.map((_, i) => ({ x: i, y: ind.ema200[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#f44336',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA610",
          data: candles.map((_, i) => ({ x: i, y: ind.ema610[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#795548',
          backgroundColor: 'transparent'
        },
        // Multi-timeframe trend indicators (if available)
        ...(multiTimeframeData ? createMultiTimeframeTrendIndicators(candles, multiTimeframeData) : [])
      ],
    },
    options: {
      responsive: false,
      plugins: {
        title: {
          display: true,
          text: `${binanceAPI.getSymbol()} ${INTERVAL.toUpperCase()} Binance`,
          color: "#FFFFFF",
          font: { size: 18, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: { color: "#DDD" },
        },
        tooltip: {
          enabled: true,
          callbacks: {
            title: function(context) {
              const index = context[0].dataIndex;
              return dayjs(candles[index].time).format('YYYY-MM-DD HH:mm');
            },
            label: function(context) {
              const index = context.dataIndex;
              const candle = candleData[index];
              if (candle && candle.o !== undefined) {
                return [
                  `Open: ${candle.o.toFixed(4)}`,
                  `High: ${candle.h.toFixed(4)}`,
                  `Low: ${candle.l.toFixed(4)}`,
                  `Close: ${candle.c.toFixed(4)}`
                ];
              }
              return context.dataset.label + ': ' + context.parsed.y;
            }
          }
        },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              // Show time labels for every few ticks
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
        y: {
          position: "left",
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

// === Multi-Timeframe Chart Indicators ===
function createMultiTimeframeTrendIndicators(candles, multiTimeframeData) {
  const indicators = [];

  // Add trend direction indicators at the top of the chart
  const highestPrice = Math.max(...candles.map(c => c.high));
  const lowestPrice = Math.min(...candles.map(c => c.low));
  const priceRange = highestPrice - lowestPrice;

  // Position trend indicators at different levels
  const trendLevels = {
    '4h': highestPrice + (priceRange * 0.05),
    '1h': highestPrice + (priceRange * 0.03),
    '15m': highestPrice + (priceRange * 0.01)
  };

  // Create trend indicator lines for each timeframe
  Object.entries(multiTimeframeData).forEach(([timeframe, data]) => {
    const trend = data.trend.trend;
    const confidence = data.trend.confidence;

    // Color based on trend
    let color = '#666666'; // Default gray
    if (trend === 'BULLISH') color = `rgba(76, 175, 80, ${confidence})`;
    else if (trend === 'BEARISH') color = `rgba(244, 67, 54, ${confidence})`;
    else color = `rgba(255, 193, 7, ${confidence})`;

    // Add horizontal line showing trend
    indicators.push({
      type: "line",
      label: `${timeframe.toUpperCase()} Trend: ${trend}`,
      data: candles.map((_, i) => ({ x: i, y: trendLevels[timeframe] })),
      borderWidth: 3,
      pointRadius: 0,
      borderColor: color,
      backgroundColor: 'transparent',
      borderDash: trend === 'SIDEWAYS' ? [5, 5] : []
    });
  });

  // Add support and resistance levels if available
  if (multiTimeframeData['15m'] && multiTimeframeData['15m'].riskLevels) {
    const riskLevels = multiTimeframeData['15m'].riskLevels;

    // Support level
    if (riskLevels.support) {
      indicators.push({
        type: "line",
        label: "Support",
        data: candles.map((_, i) => ({ x: i, y: riskLevels.support })),
        borderWidth: 2,
        pointRadius: 0,
        borderColor: 'rgba(76, 175, 80, 0.7)',
        backgroundColor: 'transparent',
        borderDash: [10, 5]
      });
    }

    // Resistance level
    if (riskLevels.resistance) {
      indicators.push({
        type: "line",
        label: "Resistance",
        data: candles.map((_, i) => ({ x: i, y: riskLevels.resistance })),
        borderWidth: 2,
        pointRadius: 0,
        borderColor: 'rgba(244, 67, 54, 0.7)',
        backgroundColor: 'transparent',
        borderDash: [10, 5]
      });
    }
  }

  return indicators;
}

async function renderRsiPanel(candles, ind, width = 1200, height = 180) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const config = {
    type: "line",
    data: {
      datasets: [
        {
          label: "RSI Upper 80",
          data: candles.map((_, i) => ({ x: i, y: 80 })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#666',
          backgroundColor: 'transparent'
        },
        {
          label: "RSI Lower 20",
          data: candles.map((_, i) => ({ x: i, y: 20 })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#666',
          backgroundColor: 'transparent'
        },
        {
          label: "RSI(14)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsi[i] })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: '#2196f3',
          backgroundColor: 'transparent'
        },
        {
          label: "EMA9(RSI)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsiEma9[i] })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#ff9800',
          backgroundColor: 'transparent'
        },
        {
          label: "WMA45(RSI)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsiWma45[i] })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: '#9c27b0',
          backgroundColor: 'transparent'
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: true, labels: { color: "#DDD" } },
        title: { display: true, text: "RSI Panel", color: "#FFF" },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          min: 0,
          max: 100,
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function renderVolumePanel(candles, width = 1200, height = 160) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const volumeData = candles.map((c, i) => {
    const bgColor = i === 0 ? "rgba(38,166,154,0.6)" :
      (c.close >= candles[i - 1].close ? "rgba(38,166,154,0.6)" : "rgba(239,83,80,0.6)");
    return { x: i, y: c.volume, backgroundColor: bgColor };
  });

  const config = {
    type: "bar",
    data: {
      datasets: [
        {
          label: "Volume",
          data: volumeData,
          borderWidth: 0,
          backgroundColor: volumeData.map(d => d.backgroundColor)
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: false },
        title: { display: true, text: "Volume", color: "#FFF" },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

// === Compose 3 panels thành 1 ảnh ===
async function composePanels(priceBuf, rsiBuf, volBuf, outPath = "chart.png") {
  const priceImg = await loadImage(priceBuf);
  const rsiImg = await loadImage(rsiBuf);
  const volImg = await loadImage(volBuf);

  const gap = 6;
  const width = Math.max(priceImg.width, rsiImg.width, volImg.width);
  const height = priceImg.height + rsiImg.height + volImg.height + gap * 2;

  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext("2d");

  // background
  ctx.fillStyle = "#0f1012";
  ctx.fillRect(0, 0, width, height);

  let y = 0;
  ctx.drawImage(priceImg, 0, y); y += priceImg.height + gap;
  ctx.drawImage(rsiImg, 0, y);   y += rsiImg.height + gap;
  ctx.drawImage(volImg, 0, y);

  const buffer = canvas.toBuffer("image/png");
  fs.writeFileSync(outPath, buffer);
  return outPath;
}

// === Enhanced Multi-Timeframe GPT Analysis ===
async function analyzeWithGPT(multiTimeframeAnalysis, tradingSignal) {
  const { '15m': tf15m, '1h': tf1h, '4h': tf4h } = multiTimeframeAnalysis;
  const last15m = tf15m.candles.at(-1);

  // Format trend information
  const formatTrend = (trend) => {
    const trendEmoji = { 'BULLISH': '🟢', 'BEARISH': '🔴', 'SIDEWAYS': '⚪' };
    return `${trendEmoji[trend.trend] || '⚪'} ${trend.trend} (${(trend.strength * 100).toFixed(0)}%)`;
  };

  // Format trading signal
  const formatSignal = (signal) => {
    const signalEmoji = {
      'STRONG_LONG': '🟢🟢', 'MODERATE_LONG': '🟢',
      'STRONG_SHORT': '🔴🔴', 'MODERATE_SHORT': '🔴',
      'WAIT': '⚪', 'NO_TRADE': '⚪'
    };
    return `${signalEmoji[signal.action] || '⚪'} ${signal.action}`;
  };

  const text = `
You are a professional trading analyst. Analyze ${binanceAPI.getSymbol()} using multi-timeframe analysis:

MULTI-TIMEFRAME TRENDS:
- 4H Trend: ${formatTrend(tf4h.trend)} (Confidence: ${(tf4h.trend.confidence * 100).toFixed(0)}%)
- 1H Trend: ${formatTrend(tf1h.trend)} (Confidence: ${(tf1h.trend.confidence * 100).toFixed(0)}%)
- 15M Trend: ${formatTrend(tf15m.trend)} (Confidence: ${(tf15m.trend.confidence * 100).toFixed(0)}%)

MAJOR TREND: ${formatTrend(tradingSignal.majorTrend)} (Strength: ${(tradingSignal.majorTrend.strength * 100).toFixed(0)}%)
ALIGNMENT: ${tradingSignal.trendAlignment.alignment} (${(tradingSignal.trendAlignment.strength * 100).toFixed(0)}%)

CURRENT DATA (15M):
- Price: ${last15m.close}
- EMA20/50/89: ${[tf15m.indicators.ema20.at(-1), tf15m.indicators.ema50.at(-1), tf15m.indicators.ema89.at(-1)].map(v=>v?.toFixed(2)).join("/")}
- RSI: ${tf15m.indicators.rsi.at(-1)?.toFixed(1)}
- Volume: ${last15m.volume}

TRADING SIGNAL: ${formatSignal(tradingSignal.recommendation)}
ENTRY SIGNAL: ${tradingSignal.entrySignal.signal}
RISK LEVELS: SL ${tradingSignal.riskLevels.stopLoss?.toFixed(2)} | TP1 ${tradingSignal.riskLevels.takeProfit1?.toFixed(2)} | RR ${tradingSignal.riskLevels.riskReward?.toFixed(2)}

RESPOND in this EXACT HTML format:

<b>📊 MULTI-TIMEFRAME ANALYSIS:</b>
• <b>Major Trend:</b> [4H+1H combined trend] - [Key reason]
• <b>Alignment:</b> [Timeframe alignment status]
• <b>15M Entry:</b> [Current 15M setup]

<b>🎯 TRADING RECOMMENDATION:</b>
• <b>Action:</b> [${tradingSignal.recommendation.action}]
• <b>Entry:</b> [Specific entry price/condition]
• <b>Stop Loss:</b> [SL level] | <b>Take Profit:</b> [TP levels]
• <b>Risk/Reward:</b> 1:${tradingSignal.riskLevels.riskReward?.toFixed(1)}

<b>💡 Confidence:</b> ${(tradingSignal.confidence * 100).toFixed(0)}%

IMPORTANT: Use only <b></b> HTML tags, no markdown. Be specific with prices. Max 500 characters.`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "You are a professional trading analyst. MUST respond in EXACT HTML format requested. Use only <b></b> tags, no markdown. Be concise and professional." },
      { role: "user", content: text },
    ],
  });

  return res.choices[0].message.content;
}

// === Legacy GPT Analysis (for backward compatibility) ===
async function legacyAnalyzeWithGPT(candles, ind) {
  const last = candles.at(-1);

  const text = `
Bạn là chuyên gia phân tích kỹ thuật. Phân tích ${binanceAPI.getSymbol()} khung ${INTERVAL} với dữ liệu:
- Giá hiện tại: ${last.close}
- EMA20/50/89: ${[ind.ema20.at(-1), ind.ema50.at(-1), ind.ema89.at(-1)].map(v=>v?.toFixed(2)).join("/")}
- RSI(14): ${ind.rsi.at(-1)?.toFixed(1)}
- Sonic R PAC: ${ind.pacC.at(-1)?.toFixed(2)}
- Volume: ${last.volume}

BẮT BUỘC trả lời theo format HTML chính xác sau:

<b>📈 XU HƯỚNG:</b> [Tăng/Giảm/Sideway] - [Lý do ngắn gọn]

<b>🎯 KỊCH BẢN TRADE:</b>
• <b>Loại:</b> [LONG 🟢 / SHORT 🔴 / NO TRADE ⚪]
• <b>Setup:</b> [Điều kiện entry cụ thể]
• <b>Xác nhận:</b> [Tín hiệu cần chờ]
• <b>Entry/SL/TP:</b> Entry [giá], SL [giá], TP [giá] (RR 1:[tỷ lệ])
• <b>Invalidation:</b> [Điều kiện hủy kèo]

<b>💡 Độ tin cậy:</b> [X]%

QUAN TRỌNG:
- Chỉ sử dụng thẻ HTML <b> và </b>
- Không dùng **, ##, hay markdown khác
- Giá phải cụ thể, không mơ hồ
- Giới hạn 600 ký tự`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "Bạn là chuyên gia trading. BẮT BUỘC trả lời theo CHÍNH XÁC format HTML được yêu cầu. Chỉ sử dụng thẻ <b></b>, không dùng ** hay markdown. Ngắn gọn, chuyên nghiệp." },
      { role: "user", content: text },
    ],
  });

  return res.choices[0].message.content;
}





// === AI Vision-Powered Trading Bot ===
async function runBot(symbol = null, targetChatId = null) {
  try {
    const analysisSymbol = symbol || binanceAPI.getSymbol();
    console.log(`🤖 Starting AI vision-powered trading analysis for ${analysisSymbol}...`);

    // Step 1: Perform multi-timeframe data collection with specific symbol
    const multiTimeframeAnalysis = await performMultiTimeframeAnalysis(analysisSymbol);

    // Step 2: AI Visual Analysis - Replace rule-based logic with AI vision
    const aiAnalysis = await performAIVisualAnalysis(multiTimeframeAnalysis, analysisSymbol);

    // Step 3: Generate AI-powered trading signal
    const aiTradingSignal = await generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis, analysisSymbol);

    // Step 4: Create traditional chart for Telegram (15M with indicators)
    const candles15m = multiTimeframeAnalysis['15m'].candles;
    const indicators15m = multiTimeframeAnalysis['15m'].indicators;

    const [priceBuf, rsiBuf, volBuf] = await Promise.all([
      renderPricePanel(candles15m, indicators15m, 1200, 560, multiTimeframeAnalysis, analysisSymbol),
      renderRsiPanel(candles15m, indicators15m),
      renderVolumePanel(candles15m),
    ]);
    const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

    // Step 5: Enhanced GPT analysis with AI vision results
    const analysis = await analyzeWithAIVision(aiAnalysis, aiTradingSignal, analysisSymbol);

    // Step 6: Create comprehensive caption with AI insights
    const caption = await createAIEnhancedCaption(aiTradingSignal, analysis, analysisSymbol);

    // Step 7: Send message with chart and AI analysis to specific chat
    const sendOptions = targetChatId ? { targetChatId } : {};
    await telegramBot.sendPhoto(
      { source: chartPath },
      caption,
      sendOptions
    );

    console.log(`✅ AI vision analysis completed successfully for ${analysisSymbol}`);
    console.log(`📊 AI Recommendation: ${aiTradingSignal.trading_recommendation.action}`);
    console.log(`🎯 AI Confidence: ${aiTradingSignal.overall_assessment.final_confidence}/10`);

  } catch (error) {
    console.error(`❌ Error in AI vision bot for ${symbol || 'default symbol'}:`, error);

    // Send critical error notification to specific chat
    const errorOptions = targetChatId ? { targetChatId } : {};
    await telegramBot.sendCriticalErrorNotification(error, 'AI Vision Trading Bot failed', errorOptions);

    // Re-throw the error to fail fast
    throw error;
  }
}

// === Caption Creation Functions ===
// These functions are now handled by TelegramBot class but kept for backward compatibility
function createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis) {
  return telegramBot.createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis);
}

async function createAIEnhancedCaption(aiTradingSignal, analysis, symbol = null) {
  return await telegramBot.createAIEnhancedCaption(aiTradingSignal, analysis, symbol);
}

// === Rule-Based Fallback Bot ===
async function runRuleBasedBot() {
  console.log('🔄 Running rule-based multi-timeframe analysis...');

  // Perform multi-timeframe analysis
  const multiTimeframeAnalysis = await performMultiTimeframeAnalysis();

  // Generate trading signal using rule-based logic
  const tradingSignal = generateTradingSignal(multiTimeframeAnalysis);

  // Use 15M data for chart rendering
  const candles15m = multiTimeframeAnalysis['15m'].candles;
  const indicators15m = multiTimeframeAnalysis['15m'].indicators;

  // Render charts
  const [priceBuf, rsiBuf, volBuf] = await Promise.all([
    renderPricePanel(candles15m, indicators15m, 1200, 560, multiTimeframeAnalysis),
    renderRsiPanel(candles15m, indicators15m),
    renderVolumePanel(candles15m),
  ]);
  const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

  // Rule-based GPT analysis
  const analysis = await analyzeWithGPT(multiTimeframeAnalysis, tradingSignal);

  // Create caption
  const caption = createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis);

  // Send message
  await telegramBot.sendPhoto(
    { source: chartPath },
    caption
  );

  console.log('✅ Rule-based analysis completed successfully');
}

// === Legacy Bot Function (fallback) ===
async function runLegacyBot() {
  const candles = await fetchData();
  const ind = calculateIndicators(candles);

  // render 3 panel
  const [priceBuf, rsiBuf, volBuf] = await Promise.all([
    renderPricePanel(candles, ind),
    renderRsiPanel(candles, ind),
    renderVolumePanel(candles),
  ]);
  const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

  // analysis
  const analysis = await legacyAnalyzeWithGPT(candles, ind);

  // Send 1 message: photo + caption with HTML formatting
  const caption = telegramBot.createLegacyCaption(INTERVAL, analysis);
  await telegramBot.sendPhoto(
    { source: chartPath },
    caption
  );
}

// === Bot now only runs on user commands (zizi + symbol) ===
// Removed automatic scheduled runs - analysis only triggered by Telegram commands

// === Exports for Testing ===
export {
  // Data functions
  fetchAllTimeframes,
  performMultiTimeframeAnalysis,
  calculateIndicators,
  analyzeTrend,
  fetchTimeframeData,
  getCachedData,
  TIMEFRAMES,
  dataCache,

  // Rule-based functions (legacy)
  generateTradingSignal,
  analyzeWithGPT,

  // AI Vision functions (new)
  generateCleanChartForAI,
  generateMultiTimeframeChartsForAI,
  analyzeChartWithAIVision,
  performAIVisualAnalysis,
  generateAITradingSignal,
  analyzeWithAIVision,

  // Bot functions
  runBot,
  runRuleBasedBot,
  runLegacyBot
};

// === AI Vision Chart Analysis System ===
async function generateCleanChartForAI(candles, indicators, timeframe, width = 1200, height = 800, symbol = null) {
  const displaySymbol = symbol || binanceAPI.getSymbol();
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#FFFFFF", // White background for better AI analysis
    chartCallback: registerAll,
  });

  const candleData = candles.map((c, i) => ({ x: i, o: c.open, h: c.high, l: c.low, c: c.close, time: c.time }));

  const config = {
    type: "line",
    data: {
      datasets: [
        // Candlestick data (will be drawn by plugin)
        {
          type: "line",
          label: `${displaySymbol} ${timeframe}`,
          data: candleData,
          showLine: false,
          pointRadius: 0,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        // Key EMAs for trend analysis
        {
          type: "line",
          label: "EMA20",
          data: candles.map((_, i) => ({ x: i, y: indicators.ema20[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 3,
          borderColor: '#2196F3', // Blue
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA50",
          data: candles.map((_, i) => ({ x: i, y: indicators.ema50[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 3,
          borderColor: '#FF9800', // Orange
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA200",
          data: candles.map((_, i) => ({ x: i, y: indicators.ema200[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 3,
          borderColor: '#F44336', // Red
          backgroundColor: 'transparent'
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        title: {
          display: true,
          text: `${binanceAPI.getSymbol()} ${timeframe.toUpperCase()} - AI Analysis Chart`,
          color: "#000000",
          font: { size: 24, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: {
            color: "#000000",
            font: { size: 14, weight: "bold" }
          },
        },
        tooltip: { enabled: false }, // Disable for cleaner image
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#000000",
            font: { size: 12, weight: "bold" },
            maxTicksLimit: 10,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: {
            color: "#E0E0E0",
            lineWidth: 1
          },
        },
        y: {
          position: "right",
          ticks: {
            color: "#000000",
            font: { size: 12, weight: "bold" }
          },
          grid: {
            color: "#E0E0E0",
            lineWidth: 1
          },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function generateMultiTimeframeChartsForAI(multiTimeframeAnalysis, symbol = null) {
  const analysisSymbol = symbol || binanceAPI.getSymbol();
  console.log(`📊 Generating AI-optimized charts for visual analysis for ${analysisSymbol}...`);

  const charts = {};
  const chartPromises = [];

  // Generate clean charts for each timeframe
  for (const [timeframe, data] of Object.entries(multiTimeframeAnalysis)) {
    const promise = generateCleanChartForAI(
      data.candles,
      data.indicators,
      timeframe,
      1200,
      800,
      analysisSymbol
    ).then(buffer => {
      const filename = `ai-chart-${analysisSymbol}-${timeframe}.png`;
      fs.writeFileSync(filename, buffer);
      charts[timeframe] = {
        buffer,
        filename,
        path: `./${filename}`
      };
      console.log(`✅ Generated ${timeframe} chart for ${analysisSymbol}: ${filename}`);
    });

    chartPromises.push(promise);
  }

  await Promise.all(chartPromises);
  return charts;
}

// === AI Visual Pattern Recognition ===
async function analyzeChartWithAIVision(chartBuffer, timeframe, currentPrice, symbol = null) {
  try {
    const analysisSymbol = symbol || binanceAPI.getSymbol();
    console.log(`🔍 Analyzing ${timeframe} chart with AI vision for ${analysisSymbol}...`);

    // Convert buffer to base64 for OpenAI Vision API
    const base64Image = chartBuffer.toString('base64');

const prompt = `Bạn là một chuyên gia phân tích kỹ thuật và là một trader chuyên giao dịch breakout, price action, và sử dụng các chỉ báo sonic R + RSI để ra quyết định.

Nhiệm vụ của bạn:
- Phân tích chi tiết biểu đồ giá ${timeframe} của ${analysisSymbol}.
- Đưa ra một kế hoạch giao dịch cụ thể, rõ ràng, giống như một trader chuyên nghiệp sẽ làm.
- Ưu tiên tìm breakout setups, phân tích price action, volume, vùng thanh khoản, và chỉ báo RSI.
- Nếu chưa có tín hiệu breakout, hãy đề xuất các kịch bản tiềm năng và các vùng giá cần theo dõi.

Thông tin đầu vào:
- Mã tài sản: ${binanceAPI.getSymbol()}
- Khung thời gian: ${timeframe}
- Giá hiện tại: ${currentPrice}

---

QUY TRÌNH PHÂN TÍCH:
1. **Xác định xu hướng chính**: trend hiện tại, sức mạnh trend, vùng giá kiểm soát.
2. **Phân tích breakout setups**:
   - Giá có đang tích lũy trước breakout không?
   - Vùng kháng cự/hỗ trợ chính.
   - Dấu hiệu trap hoặc fake breakout.
3. **Phân tích price action**:
   - Các mô hình nến quan trọng (pin bar, engulfing, doji…)
   - Các mô hình chart (flag, triangle, head & shoulders…)
   - Vùng thanh khoản cao và zone phản ứng giá.
4. **Chỉ báo**:
   - Sonic R: phân tích wave, volume và dragon trendline.
   - RSI: xác định overbought/oversold và divergence.
5. **Kế hoạch giao dịch**:
   - Đề xuất 2 kịch bản: Breakout thành công & Breakout thất bại.
   - Đưa ra entry zone, stop loss, take profit rõ ràng.
   - Tính tỷ lệ risk-reward (RRR).
   - Đưa ra mức độ tin cậy của kèo (confidence score).

---

ĐÁP ỨNG THEO ĐỊNH DẠNG JSON CHÍNH XÁC:
{
  "trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Giải thích ngắn gọn tại sao xác định xu hướng này"
  },
  "breakout_setup": {
    "status": "PENDING|CONFIRMED|FAILED",
    "key_levels": {
      "breakout_zone": "Vùng giá tiềm năng breakout",
      "fakeout_risk": "Đánh giá khả năng breakout thất bại"
    },
    "volume_analysis": "Phân tích volume quanh breakout zone",
    "breakout_direction": "UP|DOWN|UNKNOWN"
  },
  "patterns": {
    "chart_patterns": ["Danh sách mô hình chart phát hiện được"],
    "candlestick_patterns": ["Danh sách mô hình nến quan trọng"],
    "pattern_significance": "HIGH|MEDIUM|LOW"
  },
  "levels": {
    "support": [Danh sách các mức hỗ trợ quan trọng],
    "resistance": [Danh sách các mức kháng cự quan trọng],
    "key_level_proximity": "Khoảng cách giá hiện tại tới các mức key level"
  },
  "indicators": {
    "sonicR_wave": "Phân tích sóng Sonic R hiện tại",
    "dragon_trendline": "UP|DOWN|FLAT",
    "rsi_value": "Giá trị RSI hiện tại",
    "rsi_signal": "OVERBOUGHT|OVERSOLD|NEUTRAL",
    "rsi_divergence": "YES|NO"
  },
  "trading_plan": {
    "scenario_A": {
      "condition": "Kịch bản breakout thành công",
      "entry_zone": "Giá vào lệnh đề xuất",
      "stop_loss": "Mức dừng lỗ",
      "take_profit": "Mục tiêu chốt lời",
      "risk_reward": "Tỷ lệ RR",
      "confidence": 1-10
    },
    "scenario_B": {
      "condition": "Kịch bản breakout thất bại / hồi về",
      "entry_zone": "Giá vào lệnh đề xuất",
      "stop_loss": "Mức dừng lỗ",
      "take_profit": "Mục tiêu chốt lời",
      "risk_reward": "Tỷ lệ RR",
      "confidence": 1-10
    },
    "preferred_scenario": "A|B|WAIT"
  },
  "overall_assessment": {
    "market_condition": "TRENDING|RANGING|VOLATILE|CONSOLIDATING",
    "trade_quality": "HIGH|MEDIUM|LOW",
    "confidence_score": 1-10,
    "key_insights": "Những phát hiện quan trọng nhất về chart"
  }
}

QUAN TRỌNG:
- Chỉ trả lời bằng JSON hợp lệ
- Bắt đầu bằng { và kết thúc bằng }
- Không thêm văn bản ngoài JSON
- Đảm bảo tất cả string có ngoặc kép`;


    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: `data:image/png;base64,${base64Image}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 2000,
      temperature: 0.1 // Low temperature for consistent analysis
    });

    console.log(response, 'response===================')

    const analysisText = response.choices[0].message.content;

    // Check if we got a valid response
    if (!analysisText || analysisText === null || analysisText === undefined) {
      const error = new Error(`No content received from OpenAI for ${timeframe} - API returned null response`);
      console.error(`❌ ${error.message}`);

      // Send error notification to Telegram
      await telegramBot.sendErrorNotification(error, `AI Vision Analysis - ${timeframe}`);
      throw error;
    }

    // Clean and parse JSON response
    let analysis;
    try {
      let jsonText = analysisText?.trim() || "";

      // Tìm JSON nếu có bọc trong markdown
      const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonText = jsonMatch[1].trim();
      }

      // Cắt đoạn JSON từ { ... }
      const jsonStart = jsonText.indexOf("{");
      const jsonEnd = jsonText.lastIndexOf("}");
      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
      }

      console.log(`🔍 Parsing AI analysis for ${timeframe}...`);
      analysis = JSON.parse(jsonText);

      // Validate các trường quan trọng
      if (
        !analysis.trend ||
        !analysis.trading_plan ||
        !analysis.overall_assessment
      ) {
        throw new Error("Missing required fields in AI analysis");
      }
    } catch (parseError) {
      const responsePreview = analysisText
        ? analysisText.substring(0, 200)
        : "null or undefined";

      const error = new Error(`Failed to parse AI analysis for ${timeframe}: ${parseError.message}. Raw response: ${responsePreview}`);
      console.error(`❌ ${error.message}`);

      // Send error notification to Telegram
      await telegramBot.sendErrorNotification(error, `AI Vision Analysis JSON Parsing - ${timeframe}`);
      throw error;
    }

    console.log(`✅ AI analysis completed for ${timeframe}`);
    return analysis;

  } catch (error) {
    console.error(`❌ AI vision analysis failed for ${timeframe}:`, error);
    return createFallbackAnalysis(timeframe);
  }
}





// === AI-Powered Multi-Timeframe Analysis ===
async function performAIVisualAnalysis(multiTimeframeAnalysis, symbol = null) {
  try {
    const analysisSymbol = symbol || binanceAPI.getSymbol();
    console.log(`🤖 Starting AI-powered visual chart analysis for ${analysisSymbol}...`);

    // Generate clean charts for AI analysis
    const charts = await generateMultiTimeframeChartsForAI(multiTimeframeAnalysis, analysisSymbol);

    // Analyze each timeframe with AI vision
    const aiAnalysis = {};
    const analysisPromises = [];

    for (const [timeframe, chartData] of Object.entries(charts)) {
      const currentPrice = multiTimeframeAnalysis[timeframe].candles.at(-1).close;

      const promise = analyzeChartWithAIVision(
        chartData.buffer,
        timeframe,
        currentPrice,
        analysisSymbol
      ).then(analysis => {
        aiAnalysis[timeframe] = analysis;
        console.log(`✅ Successfully analyzed ${timeframe} chart for ${analysisSymbol}`);
      }).catch(error => {
        console.error(`❌ Failed to analyze ${timeframe} chart for ${analysisSymbol}:`, error.message);
        aiAnalysis[timeframe] = createFallbackAnalysis(timeframe);
      });

      analysisPromises.push(promise);
    }

    await Promise.all(analysisPromises);

    console.log(`✅ AI visual analysis completed for all timeframes for ${analysisSymbol}`);
    return aiAnalysis;

  } catch (error) {
    console.error(`❌ AI visual analysis failed for ${symbol || 'default symbol'}:`, error);
    throw error;
  }
}

// === AI-Powered Trading Signal Generation (Replaces Rule-Based Logic) ===
async function generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis, symbol = null) {
  try {
    const analysisSymbol = symbol || binanceAPI.getSymbol();
    console.log(`🎯 Generating AI-powered trading signal for ${analysisSymbol}...`);

    const { '4h': ai4h, '1h': ai1h, '15m': ai15m } = aiAnalysis;
    const currentPrice = multiTimeframeAnalysis['15m'].candles.at(-1).close;

    const j = (arr) => Array.isArray(arr) && arr.length ? arr.join(', ') : 'Không có';

    const multiTimeframePrompt = `Bạn là trader breakout theo hệ thống Sonic R + Price Action. Hãy tổng hợp đa khung (4H, 1H định hướng; 15M timing) để đưa ra khuyến nghị.

QUY ƯỚC SONIC R:
- PAC = EMA(34) High/Low/Close (DodgerBlue, fill Aqua).
- EMA89 (Signal) = Orange.
- EMA200 = Fuchsia.
- EMA610 = White.

DỮ LIỆU TÓM TẮT TỪ AI (đã phân tích từng khung):
[4H]
- Trend: ${ai4h?.trend?.direction} (S:${ai4h?.trend?.strength}/10, C:${ai4h?.trend?.confidence}/10)
- SonicR: price_vs_pac=${ai4h?.sonic_r?.price_vs_pac || 'UNKNOWN'}, pac_slope=${ai4h?.sonic_r?.pac_slope || 'UNKNOWN'}, ema89=${ai4h?.sonic_r?.ema89_relation || 'UNKNOWN'}, ema200=${ai4h?.sonic_r?.ema200_relation || 'UNKNOWN'}, ema610=${ai4h?.sonic_r?.ema610_relation || 'UNKNOWN'}, align200_610=${ai4h?.sonic_r?.trend_alignment_200_610 || 'UNKNOWN'}
- RSI: ${ai4h?.indicators?.rsi_value ?? 'N/A'} (${ai4h?.indicators?.rsi_signal || 'UNKNOWN'}), div=${ai4h?.indicators?.rsi_divergence || 'UNKNOWN'}
- Patterns: ${j(ai4h?.patterns?.chart_patterns)}
- Breakout: ${ai4h?.breakout_setup?.status || 'UNKNOWN'} ${ai4h?.breakout_setup?.breakout_direction ? '('+ai4h.breakout_setup.breakout_direction+')' : ''}, zone=${ai4h?.breakout_setup?.breakout_zone || 'N/A'}

[1H]
- Trend: ${ai1h?.trend?.direction} (S:${ai1h?.trend?.strength}/10, C:${ai1h?.trend?.confidence}/10)
- SonicR: price_vs_pac=${ai1h?.sonic_r?.price_vs_pac || 'UNKNOWN'}, pac_slope=${ai1h?.sonic_r?.pac_slope || 'UNKNOWN'}, ema89=${ai1h?.sonic_r?.ema89_relation || 'UNKNOWN'}, ema200=${ai1h?.sonic_r?.ema200_relation || 'UNKNOWN'}
- RSI: ${ai1h?.indicators?.rsi_value ?? 'N/A'} (${ai1h?.indicators?.rsi_signal || 'UNKNOWN'}), div=${ai1h?.indicators?.rsi_divergence || 'UNKNOWN'}
- Patterns: ${j(ai1h?.patterns?.chart_patterns)}
- Breakout: ${ai1h?.breakout_setup?.status || 'UNKNOWN'} ${ai1h?.breakout_setup?.breakout_direction ? '('+ai1h.breakout_setup.breakout_direction+')' : ''}, zone=${ai1h?.breakout_setup?.breakout_zone || 'N/A'}

[15M]
- Trend: ${ai15m?.trend?.direction} (S:${ai15m?.trend?.strength}/10, C:${ai15m?.trend?.confidence}/10)
- SonicR: price_vs_pac=${ai15m?.sonic_r?.price_vs_pac || 'UNKNOWN'}, pac_slope=${ai15m?.sonic_r?.pac_slope || 'UNKNOWN'}, ema89=${ai15m?.sonic_r?.ema89_relation || 'UNKNOWN'}
- RSI: ${ai15m?.indicators?.rsi_value ?? 'N/A'} (${ai15m?.indicators?.rsi_signal || 'UNKNOWN'}), div=${ai15m?.indicators?.rsi_divergence || 'UNKNOWN'}
- Patterns: ${j(ai15m?.patterns?.chart_patterns)}
- Breakout: ${ai15m?.breakout_setup?.status || 'UNKNOWN'} ${ai15m?.breakout_setup?.breakout_direction ? '('+ai15m.breakout_setup.breakout_direction+')' : ''}, zone=${ai15m?.breakout_setup?.breakout_zone || 'N/A'}

MKT HIỆN TẠI:
- SYMBOL: ${analysisSymbol}
- Price: ${currentPrice}
- 4H S/R: ${j(ai4h?.levels?.support)} / ${j(ai4h?.levels?.resistance)}
- 1H S/R: ${j(ai1h?.levels?.support)} / ${j(ai1h?.levels?.resistance)}
- 15M S/R: ${j(ai15m?.levels?.support)} / ${j(ai15m?.levels?.resistance)}

HƯỚNG DẪN:
- Ưu tiên kèo BREAKOUT theo Sonic R: giá ABOVE PAC + PAC dốc UP + đứng trên EMA89, và EMA200/610 ủng hộ (bullish) -> ưu tiên long (ngược lại cho short).
- Cảnh báo FAKEOUT nếu RSI divergence hoặc volume yếu tại điểm break.
- Đưa ra 2 kịch bản: A) Breakout thành công (entry breakout hoặc retest), B) Breakout fail/hồi về (counter or wait).
- Quản trị rủi ro chặt, có invalidation rõ ràng.

TRẢ LỜI BẰNG JSON HỢP LỆ:
{
  "major_trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Dựa trên 4H & 1H và alignment Sonic R (PAC/EMA89/200/610)"
  },
  "timeframe_alignment": {
    "alignment_score": 1-10,
    "alignment_type": "FULLY_ALIGNED|PARTIALLY_ALIGNED|CONFLICTING",
    "alignment_analysis": "Độ đồng thuận giữa 4H-1H-15M"
  },
  "breakout_analysis": {
    "setup_status": "PENDING|CONFIRMED|FAILED",
    "direction": "UP|DOWN|UNKNOWN",
    "breakout_zone": "Hộp giá/level sẽ phá",
    "retest_zone": "Vùng retest tiềm năng",
    "volume_confirmation": "YES|NO|WEAK",
    "fakeout_risk": "LOW|MEDIUM|HIGH"
  },
  "trading_recommendation": {
    "action": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL|NO_TRADE",
    "confidence": 1-10,
    "entry_price": "Giá/vùng entry (breakout hoặc retest)",
    "stop_loss": "Mức SL rõ ràng",
    "take_profit_1": "TP1",
    "take_profit_2": "TP2",
    "risk_reward_ratio": "RR ước tính",
    "position_size": "SMALL|MEDIUM|LARGE",
    "reasoning": "Lý do (Sonic R + PA)"
  },
  "trading_plan": {
    "scenario_A": {
      "condition": "Breakout thành công",
      "entry_zone": "…",
      "stop_loss": "…",
      "take_profit": ["TP1","TP2"],
      "risk_reward": "…",
      "confidence": 1-10
    },
    "scenario_B": {
      "condition": "Breakout thất bại/hồi",
      "entry_zone": "…",
      "stop_loss": "…",
      "take_profit": ["TP1","TP2"],
      "risk_reward": "…",
      "confidence": 1-10
    },
    "preferred_scenario": "A|B|WAIT"
  },
  "risk_assessment": {
    "risk_level": "LOW|MEDIUM|HIGH",
    "key_risks": ["…"],
    "invalidation_level": "Mức vô hiệu hoá kèo",
    "market_conditions": "Môi trường thị trường"
  },
  "execution_plan": {
    "entry_strategy": "Cách vào lệnh (break/close > zone, retest PAC/level…)",
    "exit_strategy": "Dời SL, trailing theo PAC/EMA89/structure",
    "monitoring_points": ["Các mốc cần theo dõi"],
    "time_horizon": "Thời gian kỳ vọng của trade"
  },
  "overall_assessment": {
    "trade_quality": "EXCELLENT|GOOD|FAIR|POOR",
    "market_opportunity": 1-10,
    "final_confidence": 1-10,
    "key_message": "Điểm mấu chốt cho trader"
  }
}

CHỈ TRẢ LỜI JSON; nếu thiếu dữ liệu hãy để UNKNOWN thay vì đoán.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "Bạn là trader breakout theo Sonic R + Price Action, ưu tiên quản trị rủi ro, không phán đoán khi dữ liệu không rõ."
        },
        { role: "user", content: multiTimeframePrompt }
      ],
      max_tokens: 2500,
      temperature: 0.1
    });

    const signalText = response.choices[0].message.content;

    // Check if we got a valid response
    if (!signalText || signalText === null || signalText === undefined) {
      const error = new Error('No content received from OpenAI for trading signal generation - API returned null response');
      console.error(`❌ ${error.message}`);

      // Send error notification to Telegram
      await telegramBot.sendErrorNotification(error, 'AI Trading Signal Generation');
      throw error;
    }

    let tradingSignal;
    try {
      let jsonText = signalText.trim();
      const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) jsonText = jsonMatch[1].trim();
      const jsonStart = jsonText.indexOf('{');
      const jsonEnd = jsonText.lastIndexOf('}');
      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
      }
      console.log('🔍 Attempting to parse AI trading signal...');
      tradingSignal = JSON.parse(jsonText);

      // Chấp nhận schema có "trading_recommendation" (ưu tiên) hoặc "trading_plan"
      if (!tradingSignal.major_trend || (!tradingSignal.trading_recommendation && !tradingSignal.trading_plan)) {
        throw new Error('Missing required fields in trading signal');
      }
    } catch (parseError) {
      const responsePreview = signalText.substring(0, 300);
      const error = new Error(`Failed to parse AI trading signal: ${parseError.message}. Raw response: ${responsePreview}`);
      console.error(`❌ ${error.message}`);

      // Send error notification to Telegram
      await telegramBot.sendErrorNotification(error, 'AI Trading Signal JSON Parsing');
      throw error;
    }

    tradingSignal.metadata = {
      analysis_type: 'AI_MULTI_TF',
      timestamp: new Date().toISOString(),
      symbol: analysisSymbol,
      current_price: currentPrice,
      timeframes_analyzed: ['4h', '1h', '15m'],
      sonic_r: { pac: 34, ema89: true, ema200: true, ema610: true }
    };

    console.log(`✅ AI trading signal generated successfully for ${analysisSymbol}`);
    return tradingSignal;

  } catch (error) {
    console.error(`❌ AI trading signal generation failed for ${symbol || 'default symbol'}:`, error);

    // Send error notification to Telegram
    await telegramBot.sendErrorNotification(error, 'AI Trading Signal Generation - System Error');
    throw error;
  }
}



// === HTML Content Cleaning for Telegram ===
function cleanHTMLForTelegram(htmlContent) {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent;
  }

  let cleaned = htmlContent;

  // Remove full HTML document structure
  cleaned = cleaned.replace(/<html[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/html>/gi, '');
  cleaned = cleaned.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
  cleaned = cleaned.replace(/<body[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/body>/gi, '');
  cleaned = cleaned.replace(/<title[^>]*>[\s\S]*?<\/title>/gi, '');

  // Remove unsupported HTML tags but keep their content
  cleaned = cleaned.replace(/<h[1-6][^>]*>/gi, '<b>');
  cleaned = cleaned.replace(/<\/h[1-6]>/gi, '</b>');
  cleaned = cleaned.replace(/<p[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/p>/gi, '\n');
  cleaned = cleaned.replace(/<div[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/div>/gi, '\n');
  cleaned = cleaned.replace(/<span[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/span>/gi, '');
  cleaned = cleaned.replace(/<strong[^>]*>/gi, '<b>');
  cleaned = cleaned.replace(/<\/strong>/gi, '</b>');
  cleaned = cleaned.replace(/<em[^>]*>/gi, '<i>');
  cleaned = cleaned.replace(/<\/em>/gi, '</i>');

  // First pass: Fix malformed opening tags before removing unsupported tags
  // Fix tags like <bTEXT or <bTEXT CONTENT where the > is missing
  cleaned = cleaned.replace(/<(b|i|u|s|a|code|pre)([A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴỶỸ][^<>]*?)(\s|$|<)/gi, '<$1>$2$3');

  // Remove any remaining unsupported tags (more comprehensive)
  cleaned = cleaned.replace(/<(?!\/?(b|i|u|s|a|code|pre)\b)[^>]*>/gi, '');

  // Remove empty tags
  cleaned = cleaned.replace(/<(\w+)><\/\1>/gi, '');
  cleaned = cleaned.replace(/<(\w+)\s*\/>/gi, '');

  // Remove malformed tags (empty angle brackets, incomplete tags)
  cleaned = cleaned.replace(/<\s*>/g, '');
  cleaned = cleaned.replace(/<\s*\/\s*>/g, '');
  cleaned = cleaned.replace(/<[^>]*$/g, ''); // Remove incomplete tags at end
  cleaned = cleaned.replace(/^[^<]*>/g, ''); // Remove incomplete tags at start

  // Fix malformed closing tags
  cleaned = cleaned.replace(/<\/\s*>/g, '');

  // Fix specific malformed tags like <bXU HƯỚNG:</b> -> <b>XU HƯỚNG:</b>
  // This handles cases where the opening tag is missing the closing >
  cleaned = cleaned.replace(/<(b|i|u|s|a|code|pre)([^<>]*?):<\/(b|i|u|s|a|code|pre)>/gi, '<$1>$2:</$3>');

  // Fix malformed opening tags that are missing the closing >
  // Pattern: <bSOMETEXT -> <b>SOMETEXT
  cleaned = cleaned.replace(/<(b|i|u|s|a|code|pre)([^<>\s]+)/gi, '<$1>$2');

  // Fix malformed opening tags followed by space: <bTEXT -> <b>TEXT
  cleaned = cleaned.replace(/<(b|i|u|s|a|code|pre)([^<>]*?)(\s)/gi, (match, tag, content, space) => {
    // Only fix if the content doesn't contain > (meaning it's not already a proper tag)
    if (!content.includes('>')) {
      return `<${tag}>${content}${space}`;
    }
    return match;
  });

  // Fix malformed tags at end of string
  cleaned = cleaned.replace(/<(b|i|u|s|a|code|pre)([^<>]*?)$/gi, (match, tag, content) => {
    if (!content.includes('>')) {
      return `<${tag}>${content}`;
    }
    return match;
  });

  // Remove any remaining angle brackets that aren't part of valid tags
  cleaned = cleaned.replace(/<(?![\/]?(?:b|i|u|s|a|code|pre)\b)[^>]*>/gi, '');

  // Fix unmatched tags - remove orphaned opening/closing tags
  // Remove orphaned closing tags (closing tags without matching opening tags)
  const supportedTags = ['b', 'i', 'u', 's', 'a', 'code', 'pre'];
  for (const tag of supportedTags) {
    // Count opening and closing tags
    const openingTags = (cleaned.match(new RegExp(`<${tag}\\b[^>]*>`, 'gi')) || []).length;
    const closingTags = (cleaned.match(new RegExp(`</${tag}>`, 'gi')) || []).length;

    // If there are more closing tags than opening tags, remove excess closing tags
    if (closingTags > openingTags) {
      const excess = closingTags - openingTags;
      for (let i = 0; i < excess; i++) {
        cleaned = cleaned.replace(new RegExp(`</${tag}>`, 'i'), '');
      }
    }

    // If there are more opening tags than closing tags, remove excess opening tags from the end
    if (openingTags > closingTags) {
      const excess = openingTags - closingTags;
      for (let i = 0; i < excess; i++) {
        const lastIndex = cleaned.lastIndexOf(`<${tag}>`);
        if (lastIndex !== -1) {
          cleaned = cleaned.substring(0, lastIndex) + cleaned.substring(lastIndex + `<${tag}>`.length);
        }
      }
    }
  }

  // Clean up extra whitespace and newlines
  cleaned = cleaned.replace(/\n\s*\n/g, '\n');
  cleaned = cleaned.replace(/^\s+|\s+$/g, '');
  cleaned = cleaned.trim();

  // Final validation - remove any remaining problematic characters
  cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  return cleaned;
}

// === Pro Trader AI Vision Analysis ===
async function analyzeWithAIVision(aiAnalysis, aiTradingSignal, symbol = null) {
  try {
    const analysisSymbol = symbol || aiTradingSignal?.metadata?.symbol || binanceAPI.getSymbol();
    const { '15m': ai15m, '1h': ai1h, '4h': ai4h } = aiAnalysis;
    const currentPrice = aiTradingSignal?.metadata?.current_price || 'N/A';

    // ===== Helper formatters =====
    const formatTrend = (trend) => {
      if (!trend || !trend.direction) return '⚪ Không rõ';
      const emoji = { BULLISH: '🟢', BEARISH: '🔴', SIDEWAYS: '⚪' };
      return `${emoji[trend.direction] || '⚪'} ${trend.direction}`;
    };

    const formatSignal = (action) => {
      if (!action) return '⚪ NO_TRADE';
      const emoji = {
        STRONG_BUY: '🟢🟢',
        BUY: '🟢',
        STRONG_SELL: '🔴🔴',
        SELL: '🔴',
        HOLD: '⚪',
        NO_TRADE: '⚪'
      };
      return `${emoji[action] || '⚪'} ${action}`;
    };

    // ===== Prompt cho GPT =====
    const text = `Bạn là một trader chuyên nghiệp. Phân tích biểu đồ đa khung thời gian dựa trên dữ liệu AI, áp dụng Sonic R, EMA 89, EMA 200, PAC, breakout và RSI divergence. Đưa ra kịch bản giao dịch rõ ràng.

FORMAT YÊU CẦU:
- Sử dụng HTML với các tag: <b>, <i>, <u> cho định dạng
- Sử dụng emoji phù hợp: 📈📉⚡🎯💡⚠️
- Cấu trúc rõ ràng với bullet points
- Tối đa 400 ký tự để hiển thị đẹp trên Telegram

DỮ LIỆU PHÂN TÍCH:

XU HƯỚNG ĐA KHUNG THỜI GIAN:
- 4H: ${formatTrend(ai4h.trend)} (${ai4h.trend.confidence || 0}/10)
- 1H: ${formatTrend(ai1h.trend)} (${ai1h.trend.confidence || 0}/10)
- 15M: ${formatTrend(ai15m.trend)} (${ai15m.trend.confidence || 0}/10)

MÔ HÌNH:
- 4H: ${ai4h.patterns?.chart_patterns?.join(', ') || 'Không có'}
- 1H: ${ai1h.patterns?.chart_patterns?.join(', ') || 'Không có'}
- 15M: ${ai15m.patterns?.chart_patterns?.join(', ') || 'Không có'}

KHUYẾN NGHỊ AI:
- Hành động: ${formatSignal(aiTradingSignal.trading_recommendation.action)}
- Entry: ${aiTradingSignal.trading_recommendation.entry_price}
- SL: ${aiTradingSignal.trading_recommendation.stop_loss}
- TP: ${aiTradingSignal.trading_recommendation.take_profit_1}
- R:R: ${aiTradingSignal.trading_recommendation.risk_reward_ratio}
- Độ tin cậy: ${aiTradingSignal.trading_recommendation.confidence}/10

YÊU CẦU FORMAT:
• Sử dụng <b>XU HƯỚNG:</b> với emoji 📈📉⚡
• Sử dụng <b>MÔ HÌNH:</b> với emoji 🔺🔻📊
• Sử dụng <b>KHUYẾN NGHỊ:</b> với emoji 🎯💡⚠️
• Entry/SL/TP với emoji 🎯🛡️🏆
• Kịch bản rõ ràng với emoji phù hợp
• Văn phong pro trader, ngắn gọn, súc tích`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "Bạn là trader chuyên nghiệp. Phân tích kỹ thuật đa khung thời gian với Sonic R, EMA 89/200, PAC, breakout, RSI divergence. Trả lời bằng HTML đẹp với emoji phù hợp, cấu trúc rõ ràng, ngắn gọn cho Telegram. Sử dụng <b>, <i>, <u> và emoji 📈📉🎯💡⚡🔺🔻📊🛡️🏆."
        },
        {
          role: "user",
          content: text
        }
      ],
      max_tokens: 900,
      temperature: 0.15
    });

    const content = response.choices?.[0]?.message?.content;

    if (!content) {
      const error = new Error('❌ No content from OpenAI for pro trader analysis');
      console.error(error.message);
      await telegramBot.sendErrorNotification(error, 'AI Pro Trader Analysis');
      throw error;
    }

    // Clean HTML content for Telegram compatibility
    let cleanedContent = cleanHTMLForTelegram(content);

    // Additional aggressive cleaning for Telegram compatibility
    cleanedContent = cleanedContent
      // Fix specific malformed patterns we see in logs like <bXU HƯỚNG:</b>
      .replace(/<([bi])([^<>]*?):<\/([bi])>/gi, '<$1>$2:</$3>')
      // Fix malformed opening tags that are missing the closing >
      .replace(/<([bi])([A-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪỬỮỰỲỴỶỸ][^<>]*?)(\s|$|[^<>])/gi, '<$1>$2$3')
      // Remove any remaining angle brackets that might be problematic
      .replace(/[<>]/g, (match, offset, string) => {
        // Only keep angle brackets that are part of valid HTML tags
        const beforeMatch = string.substring(Math.max(0, offset - 10), offset);
        const afterMatch = string.substring(offset, Math.min(string.length, offset + 10));

        // Check if this is part of a valid tag
        if (match === '<' && /^<\/?(?:b|i|u|s|a|code|pre)\b[^>]*>/.test(afterMatch)) {
          return match; // Keep valid opening tags
        }
        if (match === '>' && /<\/?(?:b|i|u|s|a|code|pre)\b[^>]*$/.test(beforeMatch + match)) {
          return match; // Keep valid closing tags
        }

        // Remove problematic angle brackets
        return '';
      })
      // Remove any remaining problematic characters
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '')
      // Clean up multiple spaces
      .replace(/\s+/g, ' ')
      .trim();

    // Debug: Log the cleaned content to identify problematic characters
    console.log('🔍 Cleaned content length:', cleanedContent.length);
    if (cleanedContent.length > 624) {
      console.log('🔍 Content around byte 624:', JSON.stringify(cleanedContent.substring(620, 630)));
      // Log character codes around the problematic area
      const chars = cleanedContent.substring(620, 630).split('').map((c, i) => `${620 + i}: '${c}' (${c.charCodeAt(0)})`);
      console.log('🔍 Character codes:', chars);
    }

    return cleanedContent;

  } catch (error) {
    console.error('❌ Pro Trader AI Vision Analysis failed:', error);
    await telegramBot.sendErrorNotification(error, 'AI Pro Trader Analysis - System Error');
    throw error;
  }
}
